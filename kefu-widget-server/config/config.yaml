server:
  address: ":8080"
  serverRoot: "resource"
  dumpRouterMap: true

database:
  default:
    host: "127.0.0.1"
    port: "3306"
    user: "root"
    pass: "123456"
    name: "kefu_server"
    type: "mysql"
    role: "master"
    debug: true
    prefix: "ks_"
    dryRun: false
    maxIdle: 10
    maxOpen: 100
    maxLifetime: "30s"

redis:
  default:
    address: "127.0.0.1:6379"
    db: 0
    pass: ""

websocket:
  port: ":8081"
  path: "/ws"
  readBufferSize: 1024
  writeBufferSize: 1024

logger:
  level: "all"
  stdout: true
  # 主日志文件配置
  file: "logs/kefu-server.log"
  # 日志轮转配置
  rotateSize: "100MB"        # 单个日志文件最大大小
  rotateExpire: "30d"        # 日志文件保留时间
  rotateBackupLimit: 10      # 最多保留的备份文件数量
  rotateBackupExpire: "30d"  # 备份文件保留时间
  rotateBackupCompress: 9    # 备份文件压缩级别 (0-9, 0=不压缩)
  rotateCheckInterval: "1h"  # 检查轮转的时间间隔
  # 日志格式配置
  format: "{Y-m-d H:i:s.u} [{Lv}] {CallerFunc}:{CallerLine} {Content}"
  # 时间格式
  timeFormat: "2006-01-02 15:04:05.000"
  # 文件日志不需要颜色
  writerColorEnable: false
  # 错误日志配置
  errorStack: true           # 错误时打印堆栈
  errorLogEnabled: true      # 启用错误日志
  accessLogEnabled: true     # 启用访问日志

  # 按级别分文件配置
  writers:
    - writer: "file"
      writerConfig:
        file: "logs/all.log"
        format: "{Y-m-d H:i:s.u} [{Lv}] {CallerFunc}:{CallerLine} {Content}"
        rotateSize: "100MB"
        rotateExpire: "30d"
        rotateBackupLimit: 10
        rotateBackupCompress: 9
    - writer: "file"
      writerConfig:
        file: "logs/error.log"
        format: "{Y-m-d H:i:s.u} [ERROR] {CallerFunc}:{CallerLine} {Content}"
        rotateSize: "50MB"
        rotateExpire: "30d"
        rotateBackupLimit: 20
        rotateBackupCompress: 9
      levels: "ERRO,FATA,PANI"
    - writer: "file"
      writerConfig:
        file: "logs/access.log"
        format: "{Y-m-d H:i:s.u} [ACCESS] {Content}"
        rotateSize: "200MB"
        rotateExpire: "7d"
        rotateBackupLimit: 5
        rotateBackupCompress: 9
      levels: "INFO"

jwt:
  signingKey: "kefu-server-jwt-signing-key"
  expire: 86400 # 24 hours

cors:
  allowOrigin: "*"
  allowMethods: "GET,POST,PUT,DELETE,OPTIONS"
  allowHeaders: "Origin,Content-Type,Accept,Authorization"
