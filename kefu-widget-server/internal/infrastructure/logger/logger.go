package logger

import (
	"context"
	"os"
	"path/filepath"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
)

// Init 初始化日志配置
func Init(ctx context.Context) error {
	// 确保日志目录存在
	logDir := "logs"
	if !gfile.Exists(logDir) {
		if err := gfile.Mkdir(logDir); err != nil {
			return err
		}
	}

	// GoFrame v2 的日志配置主要通过配置文件进行
	// 这里只需要确保日志目录存在即可
	g.Log().Info(ctx, "日志系统初始化完成")

	// 获取日志目录的绝对路径
	absPath, err := filepath.Abs(logDir)
	if err != nil {
		g.Log().Warning(ctx, "无法获取日志目录绝对路径:", err)
		absPath = logDir
	}
	g.Log().Info(ctx, "日志文件路径:", absPath)

	return nil
}

// LogError 记录错误日志
func LogError(ctx context.Context, err error, msg string, data ...interface{}) {
	if err != nil {
		g.Log().Errorf(ctx, "%s: %v, data: %+v", msg, err, data)
	} else {
		g.Log().Errorf(ctx, "%s, data: %+v", msg, data)
	}
}

// LogAccess 记录访问日志
func LogAccess(ctx context.Context, method, path, ip, userAgent string, statusCode int, duration int64) {
	g.Log().Infof(ctx, "[ACCESS] %s %s %s %s %d %dms", method, path, ip, userAgent, statusCode, duration)
}

// LogDebug 记录调试日志
func LogDebug(ctx context.Context, msg string, data ...interface{}) {
	// 只在开发环境记录调试日志
	if os.Getenv("ENV") == "development" || os.Getenv("DEBUG") == "true" {
		g.Log().Debugf(ctx, "[DEBUG] %s, data: %+v", msg, data)
	}
}
