package logger

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
)

var (
	fileLogger *log.Logger
	logFile    *os.File
)

// Init 初始化日志配置
func Init(ctx context.Context) error {
	// 确保日志目录存在
	logDir := "logs"
	if !gfile.Exists(logDir) {
		if err := gfile.Mkdir(logDir); err != nil {
			return err
		}
	}

	// 设置日志文件路径
	logFilePath := filepath.Join(logDir, "kefu-server.log")

	// 打开日志文件
	var err error
	logFile, err = os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		g.Log().Error(ctx, "无法创建日志文件:", err)
		return err
	}

	// 创建文件日志记录器
	fileLogger = log.New(logFile, "", log.LstdFlags)

	// 写入测试日志
	testLog := fmt.Sprintf("[%s] [TEST] 日志文件创建成功",
		time.Now().Format("2006-01-02 15:04:05"))
	fileLogger.Println(testLog)

	g.Log().Info(ctx, "日志系统初始化完成")
	LogInfo(ctx, "日志系统初始化完成")

	// 获取日志目录的绝对路径
	absPath, err := filepath.Abs(logDir)
	if err != nil {
		g.Log().Warning(ctx, "无法获取日志目录绝对路径:", err)
		absPath = logDir
	}
	g.Log().Info(ctx, "日志文件路径:", absPath)
	g.Log().Info(ctx, "日志文件:", logFilePath)
	LogInfo(ctx, fmt.Sprintf("日志文件路径: %s", absPath))
	LogInfo(ctx, fmt.Sprintf("日志文件: %s", logFilePath))

	return nil
}

// LogInfo 记录信息日志
func LogInfo(ctx context.Context, msg string, data ...interface{}) {
	logMsg := fmt.Sprintf("[INFO] %s", msg)
	if len(data) > 0 {
		logMsg = fmt.Sprintf("[INFO] %s, data: %+v", msg, data)
	}

	// 同时输出到控制台和文件
	if len(data) > 0 {
		g.Log().Infof(ctx, "%s, data: %+v", msg, data)
	} else {
		g.Log().Info(ctx, msg)
	}
	if fileLogger != nil {
		fileLogger.Println(logMsg)
	}
}

// LogError 记录错误日志
func LogError(ctx context.Context, err error, msg string, data ...interface{}) {
	var logMsg string
	if err != nil {
		logMsg = fmt.Sprintf("[ERROR] %s: %v", msg, err)
		if len(data) > 0 {
			logMsg = fmt.Sprintf("[ERROR] %s: %v, data: %+v", msg, err, data)
		}
		g.Log().Errorf(ctx, "%s: %v, data: %+v", msg, err, data)
	} else {
		logMsg = fmt.Sprintf("[ERROR] %s", msg)
		if len(data) > 0 {
			logMsg = fmt.Sprintf("[ERROR] %s, data: %+v", msg, data)
		}
		g.Log().Errorf(ctx, "%s, data: %+v", msg, data)
	}

	// 同时输出到文件
	if fileLogger != nil {
		fileLogger.Println(logMsg)
	}
}

// LogAccess 记录访问日志
func LogAccess(ctx context.Context, method, path, ip, userAgent string, statusCode int, duration int64) {
	logMsg := fmt.Sprintf("[ACCESS] %s %s %s %s %d %dms", method, path, ip, userAgent, statusCode, duration)
	g.Log().Infof(ctx, "[ACCESS] %s %s %s %s %d %dms", method, path, ip, userAgent, statusCode, duration)

	// 同时输出到文件
	if fileLogger != nil {
		fileLogger.Println(logMsg)
	}
}

// LogDebug 记录调试日志
func LogDebug(ctx context.Context, msg string, data ...interface{}) {
	// 只在开发环境记录调试日志
	if os.Getenv("ENV") == "development" || os.Getenv("DEBUG") == "true" {
		logMsg := fmt.Sprintf("[DEBUG] %s, data: %+v", msg, data)
		g.Log().Debugf(ctx, "[DEBUG] %s, data: %+v", msg, data)

		// 同时输出到文件
		if fileLogger != nil {
			fileLogger.Println(logMsg)
		}
	}
}

// Close 关闭日志文件
func Close() error {
	if logFile != nil {
		return logFile.Close()
	}
	return nil
}
