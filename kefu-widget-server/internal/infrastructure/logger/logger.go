package logger

import (
	"context"
	"os"
	"path/filepath"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
)

// Init 初始化日志配置
func Init(ctx context.Context) error {
	// 确保日志目录存在
	logDir := "logs"
	if !gfile.Exists(logDir) {
		if err := gfile.Mkdir(logDir); err != nil {
			return err
		}
	}

	// 获取默认日志对象
	logger := g.Log()

	// 设置日志级别
	logger.SetLevel(glog.LEVEL_ALL)

	// 设置日志文件路径
	logger.SetFile("logs/kefu-server.log")

	// 设置日志格式
	logger.SetFormat("{Y-m-d H:i:s.u} [{Lv}] {CallerFunc}:{CallerLine} {Content}")

	// 设置日志轮转
	logger.SetRotateSize(100 * 1024 * 1024) // 100MB
	logger.SetRotateExpire(30 * 24 * 3600)  // 30天
	logger.SetRotateBackupLimit(10)         // 最多保留10个备份文件
	logger.SetRotateBackupExpire(30 * 24 * 3600) // 备份文件保留30天
	logger.SetRotateBackupCompress(9)       // 压缩级别
	logger.SetRotateCheckInterval(3600)     // 1小时检查一次

	// 同时输出到控制台和文件
	logger.SetStdoutPrint(true)

	// 设置错误堆栈打印
	logger.SetStack(true)

	g.Log().Info(ctx, "日志系统初始化完成")
	g.Log().Info(ctx, "日志文件路径:", filepath.Abs(logDir))

	return nil
}

// InitAdvanced 高级日志配置 - 按级别分文件
func InitAdvanced(ctx context.Context) error {
	// 确保日志目录存在
	logDir := "logs"
	if !gfile.Exists(logDir) {
		if err := gfile.Mkdir(logDir); err != nil {
			return err
		}
	}

	// 创建不同级别的日志文件
	logFiles := map[string]string{
		"all":    "logs/all.log",
		"error":  "logs/error.log",
		"access": "logs/access.log",
		"debug":  "logs/debug.log",
	}

	// 确保所有日志文件的目录存在
	for _, logFile := range logFiles {
		dir := filepath.Dir(logFile)
		if !gfile.Exists(dir) {
			if err := gfile.Mkdir(dir); err != nil {
				return err
			}
		}
	}

	// 获取默认日志对象
	logger := g.Log()

	// 基础配置
	logger.SetLevel(glog.LEVEL_ALL)
	logger.SetStdoutPrint(true)
	logger.SetStack(true)

	// 主日志文件配置
	logger.SetFile(logFiles["all"])
	logger.SetFormat("{Y-m-d H:i:s.u} [{Lv}] {CallerFunc}:{CallerLine} {Content}")
	logger.SetRotateSize(100 * 1024 * 1024) // 100MB
	logger.SetRotateExpire(30 * 24 * 3600)  // 30天
	logger.SetRotateBackupLimit(10)
	logger.SetRotateBackupCompress(9)

	g.Log().Info(ctx, "高级日志系统初始化完成")
	g.Log().Info(ctx, "日志文件:")
	for name, path := range logFiles {
		absPath, _ := filepath.Abs(path)
		g.Log().Infof(ctx, "  %s: %s", name, absPath)
	}

	return nil
}

// LogError 记录错误日志到专门的错误文件
func LogError(ctx context.Context, err error, msg string, data ...interface{}) {
	// 创建专门的错误日志记录器
	errorLogger := glog.New()
	errorLogger.SetFile("logs/error.log")
	errorLogger.SetFormat("{Y-m-d H:i:s.u} [ERROR] {CallerFunc}:{CallerLine} {Content}")
	errorLogger.SetRotateSize(50 * 1024 * 1024) // 50MB
	errorLogger.SetRotateExpire(30 * 24 * 3600) // 30天
	errorLogger.SetRotateBackupLimit(20)
	errorLogger.SetStack(true)

	if err != nil {
		errorLogger.Errorf(ctx, "%s: %v, data: %+v", msg, err, data)
	} else {
		errorLogger.Errorf(ctx, "%s, data: %+v", msg, data)
	}
}

// LogAccess 记录访问日志
func LogAccess(ctx context.Context, method, path, ip, userAgent string, statusCode int, duration int64) {
	// 创建专门的访问日志记录器
	accessLogger := glog.New()
	accessLogger.SetFile("logs/access.log")
	accessLogger.SetFormat("{Y-m-d H:i:s.u} [ACCESS] {Content}")
	accessLogger.SetRotateSize(200 * 1024 * 1024) // 200MB
	accessLogger.SetRotateExpire(7 * 24 * 3600)   // 7天
	accessLogger.SetRotateBackupLimit(5)
	accessLogger.SetStack(false) // 访问日志不需要堆栈

	accessLogger.Infof(ctx, "%s %s %s %s %d %dms", method, path, ip, userAgent, statusCode, duration)
}

// LogDebug 记录调试日志
func LogDebug(ctx context.Context, msg string, data ...interface{}) {
	// 只在开发环境记录调试日志
	if os.Getenv("ENV") == "development" || os.Getenv("DEBUG") == "true" {
		debugLogger := glog.New()
		debugLogger.SetFile("logs/debug.log")
		debugLogger.SetFormat("{Y-m-d H:i:s.u} [DEBUG] {CallerFunc}:{CallerLine} {Content}")
		debugLogger.SetRotateSize(50 * 1024 * 1024) // 50MB
		debugLogger.SetRotateExpire(7 * 24 * 3600)  // 7天
		debugLogger.SetRotateBackupLimit(3)

		debugLogger.Debugf(ctx, "%s, data: %+v", msg, data)
	}
}

// GetLoggerByLevel 根据级别获取专门的日志记录器
func GetLoggerByLevel(level string) *glog.Logger {
	logger := glog.New()
	
	switch level {
	case "error":
		logger.SetFile("logs/error.log")
		logger.SetFormat("{Y-m-d H:i:s.u} [ERROR] {CallerFunc}:{CallerLine} {Content}")
		logger.SetRotateSize(50 * 1024 * 1024)
		logger.SetStack(true)
	case "access":
		logger.SetFile("logs/access.log")
		logger.SetFormat("{Y-m-d H:i:s.u} [ACCESS] {Content}")
		logger.SetRotateSize(200 * 1024 * 1024)
		logger.SetStack(false)
	case "debug":
		logger.SetFile("logs/debug.log")
		logger.SetFormat("{Y-m-d H:i:s.u} [DEBUG] {CallerFunc}:{CallerLine} {Content}")
		logger.SetRotateSize(50 * 1024 * 1024)
	default:
		logger.SetFile("logs/all.log")
		logger.SetFormat("{Y-m-d H:i:s.u} [{Lv}] {CallerFunc}:{CallerLine} {Content}")
		logger.SetRotateSize(100 * 1024 * 1024)
	}
	
	// 通用配置
	logger.SetRotateExpire(30 * 24 * 3600)
	logger.SetRotateBackupLimit(10)
	logger.SetRotateBackupCompress(9)
	logger.SetRotateCheckInterval(3600)
	
	return logger
}
