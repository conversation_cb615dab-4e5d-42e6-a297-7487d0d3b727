package frontend

import (
	"kefu-server/internal/service"
	"kefu-server/internal/service/visitor"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type VisitorController struct{}

func NewVisitorController() *VisitorController {
	return &VisitorController{}
}

// SignIn 访客签到
// POST /api/frontend/visitor/signin
func (c *VisitorController) SignIn(r *ghttp.Request) {
	ctx := r.Context()

	// 记录访问日志
	g.Log().Infof(ctx, "[API] 访客签到请求 - IP: %s, UserAgent: %s",
		r.<PERSON>(), r.Header.Get("User-Agent"))

	var req visitor.SignInReq
	if err := r.Parse(&req); err != nil {
		g.Log().Error(ctx, "访客签到参数解析失败:", err)
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  "参数解析失败",
			"data": nil,
		})
	}

	// 参数验证
	if err := g.Validator().Data(req).Run(ctx); err != nil {
		r.Response.WriteJsonExit(g.Map{
			"code": 400,
			"msg":  err.String(),
			"data": nil,
		})
	}

	// 从请求头中获取额外信息
	if req.UserAgent == "" {
		req.UserAgent = r.Header.Get("User-Agent")
	}
	if req.Referrer == "" {
		req.Referrer = r.Header.Get("Referer")
	}

	// 调用访客服务进行签到
	result, err := service.Visitor().SignIn(ctx, &req)
	if err != nil {
		g.Log().Error(ctx, "访客签到失败:", err)

		r.Response.WriteJsonExit(g.Map{
			"code": 500,
			"msg":  err.Error(),
			"data": nil,
		})
	}

	// 记录成功日志
	g.Log().Infof(ctx, "[API] 访客签到成功 - VisitorId: %s, SiteKey: %s, IsNew: %v",
		result.VisitorId, req.SiteKey, result.IsNewVisitor)

	r.Response.WriteJsonExit(g.Map{
		"code": 200,
		"msg":  "签到成功",
		"data": result,
	})
}
