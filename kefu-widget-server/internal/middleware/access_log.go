package middleware

import (
	"time"

	"kefu-server/internal/infrastructure/logger"

	"github.com/gogf/gf/v2/net/ghttp"
)

// AccessLog 访问日志中间件
func AccessLog(r *ghttp.Request) {
	// 记录开始时间
	startTime := time.Now()

	// 继续处理请求
	r.Middleware.Next()

	// 计算处理时间
	duration := time.Since(startTime)

	// 获取请求信息
	method := r.Method
	path := r.URL.Path
	ip := r.GetClientIp()
	userAgent := r.Header.Get("User-Agent")
	statusCode := r.Response.Status

	// 记录访问日志
	logger.LogAccess(
		r.Context(),
		method,
		path,
		ip,
		userAgent,
		statusCode,
		duration.Milliseconds(),
	)
}
